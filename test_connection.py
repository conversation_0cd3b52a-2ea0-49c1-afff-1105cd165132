#!/usr/bin/env python3
"""
连接测试脚本
用于验证服务器B的API连接和ControlNet配置
"""

import requests
import json
import sys
from typing import Dict, Any

def test_basic_connection(base_url: str) -> bool:
    """测试基础API连接"""
    try:
        response = requests.get(f"{base_url}/sdapi/v1/options", timeout=10)
        if response.status_code == 200:
            print("✅ 基础API连接成功")
            return True
        else:
            print(f"❌ API连接失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_controlnet_extension(base_url: str) -> bool:
    """测试ControlNet扩展"""
    try:
        # 测试模型列表
        response = requests.get(f"{base_url}/controlnet/model_list", timeout=10)
        if response.status_code == 200:
            models = response.json().get("model_list", [])
            print(f"✅ ControlNet扩展已安装，找到 {len(models)} 个模型")
            
            # 检查必需的模型
            required_models = [
                "control_v11f1p_sd15_depth",
                "control_v11p_sd15_lineart"
            ]
            
            found_models = []
            for model in models:
                for required in required_models:
                    if required in model:
                        found_models.append(required)
                        break
            
            print(f"📋 找到必需模型: {found_models}")
            
            if len(found_models) >= 2:
                print("✅ 所有必需的ControlNet模型都已安装")
                return True
            else:
                print("⚠️  部分必需模型缺失")
                return False
        else:
            print(f"❌ ControlNet API失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ ControlNet测试失败: {e}")
        return False

def test_controlnet_modules(base_url: str) -> bool:
    """测试ControlNet预处理器"""
    try:
        response = requests.get(f"{base_url}/controlnet/module_list", timeout=10)
        if response.status_code == 200:
            modules = response.json().get("module_list", [])
            print(f"✅ 找到 {len(modules)} 个预处理器")
            
            # 检查必需的预处理器
            required_modules = ["depth_midas", "lineart_coarse"]
            found_modules = [m for m in modules if m in required_modules]
            
            print(f"📋 找到必需预处理器: {found_modules}")
            
            if len(found_modules) >= 2:
                print("✅ 所有必需的预处理器都可用")
                return True
            else:
                print("⚠️  部分必需预处理器缺失")
                return False
        else:
            print(f"❌ 预处理器API失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 预处理器测试失败: {e}")
        return False

def test_samplers(base_url: str) -> bool:
    """测试采样器"""
    try:
        response = requests.get(f"{base_url}/sdapi/v1/samplers", timeout=10)
        if response.status_code == 200:
            samplers = response.json()
            sampler_names = [s["name"] for s in samplers]
            print(f"✅ 找到 {len(samplers)} 个采样器")
            print(f"📋 可用采样器: {sampler_names[:5]}...")  # 显示前5个
            return True
        else:
            print(f"❌ 采样器API失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 采样器测试失败: {e}")
        return False

def test_models(base_url: str) -> bool:
    """测试SD模型"""
    try:
        response = requests.get(f"{base_url}/sdapi/v1/sd-models", timeout=10)
        if response.status_code == 200:
            models = response.json()
            if models:
                print(f"✅ 找到 {len(models)} 个SD模型")
                current_model = models[0].get("title", "未知")
                print(f"📋 当前模型: {current_model}")
                return True
            else:
                print("❌ 未找到SD模型")
                return False
        else:
            print(f"❌ 模型API失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return False

def test_simple_generation(base_url: str) -> bool:
    """测试简单图像生成"""
    print("🧪 测试简单图像生成...")
    
    payload = {
        "prompt": "test image",
        "negative_prompt": "",
        "width": 64,
        "height": 64,
        "steps": 1,
        "cfg_scale": 7.0,
        "sampler_name": "Euler a",
        "seed": -1,
        "batch_size": 1,
        "n_iter": 1,
        "do_not_save_samples": True,
        "do_not_save_grid": True
    }
    
    try:
        response = requests.post(
            f"{base_url}/sdapi/v1/txt2img",
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("images"):
                print("✅ 图像生成测试成功")
                return True
            else:
                print("❌ 生成结果为空")
                return False
        else:
            print(f"❌ 生成测试失败: HTTP {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 生成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = input("请输入服务器B的URL (例如: http://*************:7860): ").strip()
    
    if not base_url:
        base_url = "http://localhost:7860"
    
    print(f"🔍 测试服务器: {base_url}")
    print("=" * 50)
    
    tests = [
        ("基础连接", test_basic_connection),
        ("ControlNet扩展", test_controlnet_extension),
        ("ControlNet预处理器", test_controlnet_modules),
        ("采样器", test_samplers),
        ("SD模型", test_models),
        ("简单生成", test_simple_generation)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 测试 {test_name}...")
        try:
            result = test_func(base_url)
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！服务器配置正确。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查服务器配置。")
        return 1

if __name__ == "__main__":
    exit(main())
