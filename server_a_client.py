#!/usr/bin/env python3
"""
服务器A - 客户端代码
用于向服务器B的Stable Diffusion WebUI发送API请求
包含双层ControlNet（Depth + LineArt）支持
"""

import requests
import json
import base64
import io
from PIL import Image
import time
from typing import Optional, Dict, Any

class StableDiffusionClient:
    def __init__(self, server_b_url: str = "http://server-b-ip:7860"):
        """
        初始化SD客户端
        
        Args:
            server_b_url: 服务器B的WebUI地址
        """
        self.base_url = server_b_url
        self.api_url = f"{server_b_url}/sdapi/v1"
        
    def encode_image_to_base64(self, image_path: str) -> str:
        """将图像文件编码为base64字符串"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    def pil_to_base64(self, image: Image.Image) -> str:
        """将PIL图像转换为base64字符串"""
        buffer = io.BytesIO()
        image.save(buffer, format="PNG")
        return base64.b64encode(buffer.getvalue()).decode('utf-8')
    
    def base64_to_pil(self, base64_str: str) -> Image.Image:
        """将base64字符串转换为PIL图像"""
        image_data = base64.b64decode(base64_str)
        return Image.open(io.BytesIO(image_data))
    
    def check_api_status(self) -> bool:
        """检查API是否可用"""
        try:
            response = requests.get(f"{self.api_url}/options", timeout=10)
            return response.status_code == 200
        except:
            return False
    
    def get_controlnet_models(self) -> list:
        """获取可用的ControlNet模型列表"""
        try:
            response = requests.get(f"{self.base_url}/controlnet/model_list")
            if response.status_code == 200:
                return response.json()["model_list"]
        except:
            pass
        return []
    
    def get_controlnet_modules(self) -> list:
        """获取可用的ControlNet预处理器模块"""
        try:
            response = requests.get(f"{self.base_url}/controlnet/module_list")
            if response.status_code == 200:
                return response.json()["module_list"]
        except:
            pass
        return []
    
    def txt2img_with_controlnet(
        self,
        prompt: str,
        negative_prompt: str = "",
        depth_image_path: Optional[str] = None,
        lineart_image_path: Optional[str] = None,
        width: int = 512,
        height: int = 512,
        steps: int = 20,
        cfg_scale: float = 7.0,
        sampler_name: str = "Euler a",
        seed: int = -1,
        batch_size: int = 1,
        **kwargs
    ) -> Dict[str, Any]:
        """
        使用双层ControlNet进行文本到图像生成
        
        Args:
            prompt: 正向提示词
            negative_prompt: 负向提示词
            depth_image_path: 深度控制图像路径
            lineart_image_path: 线稿控制图像路径
            width: 图像宽度
            height: 图像高度
            steps: 采样步数
            cfg_scale: CFG引导强度
            sampler_name: 采样器名称
            seed: 随机种子
            batch_size: 批次大小
            **kwargs: 其他参数
        
        Returns:
            包含生成图像和元数据的字典
        """
        
        # 构建基础请求数据
        payload = {
            "prompt": prompt,
            "negative_prompt": negative_prompt,
            "width": width,
            "height": height,
            "steps": steps,
            "cfg_scale": cfg_scale,
            "sampler_name": sampler_name,
            "seed": seed,
            "batch_size": batch_size,
            "n_iter": 1,
            "restore_faces": False,
            "tiling": False,
            "do_not_save_samples": True,
            "do_not_save_grid": True,
        }
        
        # 添加其他参数
        payload.update(kwargs)
        
        # 构建ControlNet参数
        controlnet_units = []
        
        # 添加Depth ControlNet
        if depth_image_path:
            depth_image_b64 = self.encode_image_to_base64(depth_image_path)
            depth_unit = {
                "input_image": depth_image_b64,
                "module": "depth_midas",  # 深度预处理器
                "model": "control_v11f1p_sd15_depth [cfd03158]",  # 深度模型
                "weight": 1.0,
                "resize_mode": "Crop and Resize",
                "lowvram": False,
                "processor_res": 512,
                "threshold_a": 64,
                "threshold_b": 64,
                "guidance_start": 0.0,
                "guidance_end": 1.0,
                "control_mode": "Balanced",
                "pixel_perfect": False
            }
            controlnet_units.append(depth_unit)
        
        # 添加LineArt ControlNet
        if lineart_image_path:
            lineart_image_b64 = self.encode_image_to_base64(lineart_image_path)
            lineart_unit = {
                "input_image": lineart_image_b64,
                "module": "lineart_coarse",  # 线稿预处理器
                "model": "control_v11p_sd15_lineart [43d4be0d]",  # 线稿模型
                "weight": 1.0,
                "resize_mode": "Crop and Resize",
                "lowvram": False,
                "processor_res": 512,
                "threshold_a": 64,
                "threshold_b": 64,
                "guidance_start": 0.0,
                "guidance_end": 1.0,
                "control_mode": "Balanced",
                "pixel_perfect": False
            }
            controlnet_units.append(lineart_unit)
        
        # 添加ControlNet参数到payload
        if controlnet_units:
            payload["alwayson_scripts"] = {
                "controlnet": {
                    "args": controlnet_units
                }
            }
        
        # 发送请求
        try:
            response = requests.post(
                f"{self.api_url}/txt2img",
                json=payload,
                timeout=300  # 5分钟超时
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "images": result.get("images", []),
                    "parameters": result.get("parameters", {}),
                    "info": result.get("info", "")
                }
            else:
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}"
                }
                
        except requests.exceptions.Timeout:
            return {
                "success": False,
                "error": "请求超时"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"请求失败: {str(e)}"
            }
    
    def save_images(self, images_b64: list, output_dir: str = "output") -> list:
        """保存生成的图像"""
        import os
        os.makedirs(output_dir, exist_ok=True)
        
        saved_paths = []
        for i, img_b64 in enumerate(images_b64):
            image = self.base64_to_pil(img_b64)
            filename = f"generated_{int(time.time())}_{i}.png"
            filepath = os.path.join(output_dir, filename)
            image.save(filepath)
            saved_paths.append(filepath)
        
        return saved_paths

# 使用示例
def main():
    # 初始化客户端
    client = StableDiffusionClient("http://192.168.1.100:7860")  # 替换为实际的服务器B地址
    
    # 检查API状态
    if not client.check_api_status():
        print("错误: 无法连接到服务器B的API")
        return
    
    print("成功连接到服务器B")
    
    # 获取可用模型（可选）
    models = client.get_controlnet_models()
    print(f"可用ControlNet模型: {models[:5]}...")  # 显示前5个
    
    # 生成图像
    result = client.txt2img_with_controlnet(
        prompt="a beautiful landscape, highly detailed, 8k",
        negative_prompt="blurry, low quality, distorted",
        depth_image_path="input/depth_image.jpg",  # 深度控制图像
        lineart_image_path="input/lineart_image.jpg",  # 线稿控制图像
        width=512,
        height=512,
        steps=20,
        cfg_scale=7.0,
        seed=42
    )
    
    if result["success"]:
        print("图像生成成功!")
        saved_paths = client.save_images(result["images"])
        print(f"图像已保存到: {saved_paths}")
    else:
        print(f"生成失败: {result['error']}")

if __name__ == "__main__":
    main()
