# Stable Diffusion WebUI API 双层ControlNet 调用指南

本项目提供了通过API调用Stable Diffusion WebUI的完整解决方案，支持双层ControlNet（Depth + LineArt）控制。

## 📁 文件结构

```
api_examples/
├── README.md                    # 本文档
├── server_a_client.py          # 服务器A客户端代码（基础版）
├── advanced_client.py          # 高级客户端代码（功能完整）
├── server_b_setup.md           # 服务器B配置指南
├── json_request_examples.json  # JSON请求示例
└── requirements.txt            # Python依赖
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装Python依赖
pip install -r requirements.txt

# 创建必要目录
mkdir -p input output
```

### 2. 服务器B配置

按照 `server_b_setup.md` 中的说明配置Stable Diffusion WebUI：

1. 安装ControlNet扩展
2. 下载必需的模型文件
3. 启用API并配置网络访问
4. 验证安装

### 3. 准备输入图像

将控制图像放入 `input/` 目录：
- `depth_image.jpg` - 深度控制图像
- `lineart_image.jpg` - 线稿控制图像

### 4. 运行客户端

```bash
# 基础客户端
python server_a_client.py

# 高级客户端（推荐）
python advanced_client.py
```

## 📖 详细说明

### ControlNet 配置参数

#### Depth ControlNet
- **预处理器**: `depth_midas`, `depth_leres`, `depth_zoe`
- **模型**: `control_v11f1p_sd15_depth [cfd03158]`
- **用途**: 控制图像的深度和空间结构

#### LineArt ControlNet  
- **预处理器**: `lineart_coarse`, `lineart_realistic`, `lineart_anime`
- **模型**: `control_v11p_sd15_lineart [43d4be0d]`
- **用途**: 控制图像的线条和轮廓

### 关键参数说明

| 参数 | 说明 | 推荐值 |
|------|------|--------|
| `weight` | ControlNet影响强度 | 0.5-1.5 |
| `guidance_start` | 开始引导的步数比例 | 0.0-0.3 |
| `guidance_end` | 结束引导的步数比例 | 0.7-1.0 |
| `control_mode` | 控制模式 | "Balanced" |
| `processor_res` | 预处理器分辨率 | 512/768 |

### API端点

| 端点 | 方法 | 说明 |
|------|------|------|
| `/sdapi/v1/txt2img` | POST | 文本生成图像 |
| `/sdapi/v1/img2img` | POST | 图像到图像 |
| `/sdapi/v1/progress` | GET | 获取生成进度 |
| `/sdapi/v1/interrupt` | POST | 中断生成 |
| `/controlnet/model_list` | GET | ControlNet模型列表 |
| `/controlnet/module_list` | GET | 预处理器列表 |

## 🔧 高级功能

### 1. 批量处理

```python
# 批量生成多个图像
prompts = [
    "a beautiful landscape",
    "a portrait of a woman", 
    "a futuristic city"
]

results = client.batch_generate(prompts, controlnet_configs)
```

### 2. 进度监控

```python
# 监控生成进度
def progress_callback(progress):
    if progress.get('active'):
        percent = progress.get('progress', 0) * 100
        print(f"进度: {percent:.1f}%")

client.monitor_progress(progress_callback)
```

### 3. 图像预处理

```python
# 自动调整图像大小和增强
image = client.preprocess_image("input.jpg", (512, 512))
enhanced = client.enhance_image(image, brightness=1.1, contrast=1.2)
```

### 4. 错误处理

```python
result = client.txt2img_with_controlnet(...)

if result["success"]:
    print("生成成功!")
    images = result["images"]
else:
    print(f"生成失败: {result['error']}")
```

## 🛠️ 故障排除

### 常见问题

1. **连接失败**
   - 检查服务器B的IP地址和端口
   - 确认防火墙设置
   - 验证API是否启用

2. **ControlNet模型未找到**
   - 确认模型文件已下载到正确目录
   - 检查模型文件名是否正确
   - 重启WebUI

3. **内存不足**
   - 降低图像分辨率
   - 使用 `--lowvram` 启动参数
   - 减少批次大小

4. **生成质量差**
   - 调整ControlNet权重
   - 优化提示词
   - 调整CFG Scale和步数

### 性能优化

1. **网络优化**
   ```python
   # 使用会话保持连接
   session = requests.Session()
   session.timeout = 300
   ```

2. **图像压缩**
   ```python
   # 压缩输入图像减少传输时间
   image = image.resize((512, 512), Image.LANCZOS)
   ```

3. **并发控制**
   ```python
   # 避免同时发送过多请求
   time.sleep(1)  # 请求间隔
   ```

## 📝 示例代码

### 基础调用示例

```python
from server_a_client import StableDiffusionClient

client = StableDiffusionClient("http://*************:7860")

result = client.txt2img_with_controlnet(
    prompt="a beautiful landscape, highly detailed",
    depth_image_path="input/depth.jpg",
    lineart_image_path="input/lineart.jpg",
    width=512,
    height=512,
    steps=20
)

if result["success"]:
    client.save_images(result["images"], "output")
```

### 高级调用示例

```python
from advanced_client import AdvancedSDClient

client = AdvancedSDClient("http://*************:7860")

# 创建ControlNet配置
controlnet_units = [
    client.create_controlnet_unit(
        image_path="input/depth.jpg",
        control_type="depth",
        weight=1.0
    ),
    client.create_controlnet_unit(
        image_path="input/lineart.jpg", 
        control_type="lineart",
        weight=0.8
    )
]

result = client.txt2img_with_controlnet(
    prompt="masterpiece, best quality, detailed",
    controlnet_units=controlnet_units,
    steps=25,
    cfg_scale=7.5
)
```

## 📚 参考资源

- [Stable Diffusion WebUI](https://github.com/AUTOMATIC1111/stable-diffusion-webui)
- [ControlNet Extension](https://github.com/Mikubill/sd-webui-controlnet)
- [ControlNet Models](https://huggingface.co/lllyasviel/ControlNet-v1-1)
- [API Documentation](https://github.com/AUTOMATIC1111/stable-diffusion-webui/wiki/API)

## 📄 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。
