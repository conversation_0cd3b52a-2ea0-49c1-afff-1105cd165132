{"basic_txt2img_with_dual_controlnet": {"prompt": "a beautiful landscape with mountains and lake, highly detailed, 8k, masterpiece", "negative_prompt": "blurry, low quality, distorted, ugly, bad anatomy", "width": 512, "height": 512, "steps": 20, "cfg_scale": 7.0, "sampler_name": "Euler a", "seed": -1, "batch_size": 1, "n_iter": 1, "restore_faces": false, "tiling": false, "do_not_save_samples": true, "do_not_save_grid": true, "alwayson_scripts": {"controlnet": {"args": [{"input_image": "base64_encoded_depth_image_here", "module": "depth_midas", "model": "control_v11f1p_sd15_depth [cfd03158]", "weight": 1.0, "resize_mode": "Crop and Resize", "lowvram": false, "processor_res": 512, "threshold_a": 64, "threshold_b": 64, "guidance_start": 0.0, "guidance_end": 1.0, "control_mode": "Balanced", "pixel_perfect": false}, {"input_image": "base64_encoded_lineart_image_here", "module": "lineart_coarse", "model": "control_v11p_sd15_lineart [43d4be0d]", "weight": 1.0, "resize_mode": "Crop and Resize", "lowvram": false, "processor_res": 512, "threshold_a": 64, "threshold_b": 64, "guidance_start": 0.0, "guidance_end": 1.0, "control_mode": "Balanced", "pixel_perfect": false}]}}}, "advanced_controlnet_config": {"prompt": "portrait of a beautiful woman, detailed face, professional photography, studio lighting", "negative_prompt": "blurry, low quality, distorted, ugly, bad anatomy, extra limbs", "width": 768, "height": 768, "steps": 30, "cfg_scale": 8.0, "sampler_name": "DPM++ 2M <PERSON>", "seed": 42, "batch_size": 1, "n_iter": 1, "restore_faces": true, "tiling": false, "enable_hr": true, "hr_scale": 1.5, "hr_upscaler": "Latent", "denoising_strength": 0.7, "alwayson_scripts": {"controlnet": {"args": [{"input_image": "base64_encoded_depth_image_here", "module": "depth_midas", "model": "control_v11f1p_sd15_depth [cfd03158]", "weight": 0.8, "resize_mode": "Crop and Resize", "lowvram": false, "processor_res": 768, "threshold_a": 64, "threshold_b": 64, "guidance_start": 0.0, "guidance_end": 0.8, "control_mode": "My prompt is more important", "pixel_perfect": true}, {"input_image": "base64_encoded_lineart_image_here", "module": "lineart_realistic", "model": "control_v11p_sd15_lineart [43d4be0d]", "weight": 0.6, "resize_mode": "Crop and Resize", "lowvram": false, "processor_res": 768, "threshold_a": 64, "threshold_b": 64, "guidance_start": 0.2, "guidance_end": 1.0, "control_mode": "ControlNet is more important", "pixel_perfect": true}]}}}, "img2img_with_controlnet": {"init_images": ["base64_encoded_init_image_here"], "prompt": "enhance this image, add more details, professional quality", "negative_prompt": "blurry, low quality, artifacts", "width": 512, "height": 512, "steps": 25, "cfg_scale": 7.5, "sampler_name": "Euler a", "seed": -1, "denoising_strength": 0.6, "batch_size": 1, "n_iter": 1, "alwayson_scripts": {"controlnet": {"args": [{"input_image": "base64_encoded_depth_image_here", "module": "depth_midas", "model": "control_v11f1p_sd15_depth [cfd03158]", "weight": 0.7, "resize_mode": "Crop and Resize", "lowvram": false, "processor_res": 512, "threshold_a": 64, "threshold_b": 64, "guidance_start": 0.0, "guidance_end": 1.0, "control_mode": "Balanced", "pixel_perfect": false}]}}}, "controlnet_preprocessor_options": {"depth_modules": ["depth_midas", "depth_leres", "depth_leres++", "depth_zoe"], "lineart_modules": ["lineart_coarse", "lineart_realistic", "lineart_anime", "lineart_standard"], "other_modules": ["canny", "openpose", "openpose_hand", "openpose_face", "openpose_faceonly", "openpose_full", "scribble_pidinet", "scribble_hed", "softedge_pidinet", "softedge_hed", "normal_bae", "normal_midas", "seg_ofade20k", "seg_ofcoco"]}, "controlnet_models": {"depth_models": ["control_v11f1p_sd15_depth [cfd03158]", "control_v11p_sd15_depth [e31a7c4e]"], "lineart_models": ["control_v11p_sd15_lineart [43d4be0d]", "control_v11p_sd15_lineart_anime [5e915f3a]"], "other_models": ["control_v11p_sd15_canny [d14c016b]", "control_v11p_sd15_openpose [cab727d4]", "control_v11p_sd15_scribble [d4ba51ff]", "control_v11p_sd15_softedge [a8575a2a]", "control_v11p_sd15_normalbae [316696f1]", "control_v11p_sd15_seg [e1f51eb9]"]}, "control_modes": ["Balanced", "My prompt is more important", "ControlNet is more important"], "resize_modes": ["Just Resize", "Crop and Resize", "Resize and Fill"], "response_format": {"images": ["base64_encoded_image_1", "base64_encoded_image_2"], "parameters": {"prompt": "used_prompt", "negative_prompt": "used_negative_prompt", "width": 512, "height": 512, "steps": 20, "cfg_scale": 7.0, "sampler_name": "Euler a", "seed": 1234567890}, "info": "detailed_generation_info_json_string"}}