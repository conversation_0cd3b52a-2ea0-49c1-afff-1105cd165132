# 服务器B - Stable Diffusion WebUI 配置指南

## 1. 安装ControlNet扩展

### 方法一：通过WebUI界面安装
1. 启动WebUI
2. 进入 `Extensions` 标签页
3. 点击 `Available` 子标签
4. 搜索 "controlnet"
5. 找到 `sd-webui-controlnet` 扩展并点击 `Install`
6. 重启WebUI

### 方法二：手动安装
```bash
cd webui/extensions
git clone https://github.com/Mikubill/sd-webui-controlnet.git
```

## 2. 下载ControlNet模型

### 创建模型目录
```bash
mkdir -p webui/extensions/sd-webui-controlnet/models
mkdir -p webui/models/ControlNet
```

### 下载必需的模型文件
下载以下模型到 `webui/models/ControlNet/` 目录：

**Depth模型:**
- `control_v11f1p_sd15_depth.pth`
- `control_v11f1p_sd15_depth.yaml`

**LineArt模型:**
- `control_v11p_sd15_lineart.pth` 
- `control_v11p_sd15_lineart.yaml`

### 下载链接
```bash
# Depth模型
wget https://huggingface.co/lllyasviel/ControlNet-v1-1/resolve/main/control_v11f1p_sd15_depth.pth
wget https://huggingface.co/lllyasviel/ControlNet-v1-1/resolve/main/control_v11f1p_sd15_depth.yaml

# LineArt模型
wget https://huggingface.co/lllyasviel/ControlNet-v1-1/resolve/main/control_v11p_sd15_lineart.pth
wget https://huggingface.co/lllyasviel/ControlNet-v1-1/resolve/main/control_v11p_sd15_lineart.yaml
```

## 3. 启动WebUI并启用API

### 修改启动脚本
编辑 `webui-user.bat` (Windows) 或 `webui-user.sh` (Linux):

```bash
# Windows (webui-user.bat)
set COMMANDLINE_ARGS=--api --listen --port 7860

# Linux (webui-user.sh)
export COMMANDLINE_ARGS="--api --listen --port 7860"
```

### 启动参数说明
- `--api`: 启用API接口
- `--listen`: 允许外部访问（默认只允许本地访问）
- `--port 7860`: 指定端口（默认7860）
- `--cors-allow-origins=*`: 允许跨域请求（如需要）

### 完整启动命令示例
```bash
python launch.py --api --listen --port 7860 --cors-allow-origins=*
```

## 4. 验证安装

### 检查API端点
访问以下URL验证API是否正常工作：
- `http://server-b-ip:7860/docs` - API文档
- `http://server-b-ip:7860/sdapi/v1/options` - 获取配置
- `http://server-b-ip:7860/controlnet/model_list` - ControlNet模型列表
- `http://server-b-ip:7860/controlnet/module_list` - 预处理器列表

### 测试ControlNet
```python
import requests

# 测试ControlNet模型列表
response = requests.get("http://server-b-ip:7860/controlnet/model_list")
print("ControlNet模型:", response.json())

# 测试预处理器列表
response = requests.get("http://server-b-ip:7860/controlnet/module_list")
print("预处理器:", response.json())
```

## 5. 防火墙配置

### Windows防火墙
```cmd
netsh advfirewall firewall add rule name="SD WebUI" dir=in action=allow protocol=TCP localport=7860
```

### Linux iptables
```bash
sudo iptables -A INPUT -p tcp --dport 7860 -j ACCEPT
sudo iptables-save > /etc/iptables/rules.v4
```

### Ubuntu UFW
```bash
sudo ufw allow 7860/tcp
```

## 6. 性能优化建议

### GPU内存优化
```bash
# 低显存模式
--lowvram --precision full --no-half

# 中等显存模式  
--medvram --precision autocast

# 高显存模式（推荐）
--precision autocast --no-half-vae
```

### CPU优化
```bash
# 多线程优化
--threads 4

# 禁用不必要的功能
--disable-console-progressbars --disable-safe-unpickle
```

## 7. 常见问题解决

### ControlNet模型未加载
1. 检查模型文件是否在正确目录
2. 确认文件权限
3. 重启WebUI
4. 查看控制台错误信息

### API连接失败
1. 检查防火墙设置
2. 确认端口是否被占用
3. 验证IP地址和端口
4. 检查网络连通性

### 内存不足
1. 降低图像分辨率
2. 使用 `--lowvram` 参数
3. 减少批次大小
4. 关闭其他应用程序

## 8. 监控和日志

### 启用详细日志
```bash
python launch.py --api --listen --log-level DEBUG
```

### 监控GPU使用
```bash
# NVIDIA GPU
nvidia-smi -l 1

# AMD GPU  
rocm-smi -l 1
```

### 监控系统资源
```bash
htop
iostat -x 1
```
