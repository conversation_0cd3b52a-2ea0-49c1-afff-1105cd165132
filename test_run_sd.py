import requests
import base64
from PIL import Image
import io
import time

# ========== 配置 ==========
SERVER_A_URL = "http://10.20.174.41:5001/generate"  # Flask 接口地址
TIF_PATH = r"D:\BIGEMAP\BMDownload\CD_shot1\CD_shot1_Level_17.tif"
PROMPT_CN = "现代化的城市，道路干净，绿化优美，环境宜人,基础设施完善"
NEGATIVE_PROMPT_CN = "裸地，建筑用地"

# 提示词
PROMPT = {
    "prompt": PROMPT_CN,
    "negative_prompt": NEGATIVE_PROMPT_CN
}

# 普通参数 + ControlNet 参数
PARAMS = {
    "steps": 30,
    "cfg_scale": 5.0,
    "sampler_name": "Euler a",
    "denoising_strength": 0.2,
    # "controlnet": {
    #     "enabled": True,
    #     "module": "canny",          # 预处理器模块
    #     "model": "control_canny-fp16 [e3fe7712]",  # ControlNet 模型名称
    #     "weight": 1.0,
    #     "guidance_start": 0.0,
    #     "guidance_end": 1.0,
    #     "resize_mode": "Just Resize",
    #     "input_image": None         # 可以填图像（base64），若不传则用主输入图
    # }
}

# ========== 函数 ==========
def tif_to_base64(tif_path):
    """TIF 转 PNG 并转为 base64"""
    img = Image.open(tif_path).convert("RGB")
    buf = io.BytesIO()
    img.save(buf, format="PNG")
    width, height = img.size
    return base64.b64encode(buf.getvalue()).decode("utf-8"), width, height

def send_request():
    img_base64, width, height = tif_to_base64(TIF_PATH)
    # PARAMS["controlnet"]["input_image"] = img_base64  # 这里让ControlNet使用同一张输入图
    PARAMS["width"] = width
    PARAMS["height"] = height

    payload = {
        "image": img_base64,
        "prompt": PROMPT,
        "params": PARAMS
    }
    start_time = time.time()
    response = requests.post(SERVER_A_URL, json=payload)
    result = response.json()

    # 保存结果
    img_base64 = result["result"]
    img_bytes = base64.b64decode(img_base64)

    end_time = time.time()
    with open("D:/sd.webui/temp_rslt/result.png", "wb") as f:
        f.write(img_bytes)
    timeConsum = end_time - start_time
    print(f"✅ 结果已保存 result.png, 耗时{timeConsum:.2f} s")

if __name__ == "__main__":
    send_request()
