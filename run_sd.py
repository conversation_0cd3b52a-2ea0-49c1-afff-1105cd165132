from flask import Flask, request, jsonify
import requests, base64, io
from PIL import Image
from deep_translator import GoogleTranslator  # 简单翻译用
import os

app = Flask(__name__)

# Stable Diffusion WebUI API 地址
SD_API_URL = "http://127.0.0.1:7860/sdapi/v1/img2img"

# ========== 工具函数 ==========
def translate_prompt(prompt_cn: str) -> str:
    """翻译中文 Prompt 到英文"""
    try:
        return GoogleTranslator(source='auto', target='en').translate(prompt_cn)
    except:
        return prompt_cn  # 出错则原样返回

def call_stable_diffusion(img_base64, prompt_en, params):
    controlnet_args = {
        "ControlNet": {
            "args": [
                {
                    "input_image": img_base64,
                    "control_mode": "Balanced",
                    "module": "canny",
                    "model": "control_v11f1p_sd15_depth [cfd03158]",
                    "resize_mode": "Crop and Resize",
                    "lowvram": False,
                    "processor_res": 512,
                    "threshold_a": 64,
                    "threshold_b": 64,
                    "weight": 1.0,
                    "guidance_start": 0.0,
                    "guidance_end": 1.0,
                    "pixel_perfect": True,
                    "enabled": True,
                }
            ]
        }
    }
    
    #     "ControlNet": {
    #     "args": [
    #         {
    #             "batch_images": "",
    #             "control_mode": "Balanced",
    #             "enabled": True,
    #             "guidance_end": 1,
    #             "guidance_start": 0,
    #             "image": {
    #                 "image": encode_file_to_base64(r"B:\path\to\control\img.png"),
    #                 "mask": None  # base64, None when not need
    #             },
    #             "input_mode": "simple",
    #             "is_ui": True,
    #             "loopback": False,
    #             "low_vram": False,
    #             "model": "control_v11p_sd15_canny [d14c016b]",
    #             "module": "canny",
    #             "output_dir": "",
    #             "pixel_perfect": False,
    #             "processor_res": 512,
    #             "resize_mode": "Crop and Resize",
    #             "threshold_a": 100,
    #             "threshold_b": 200,
    #             "weight": 1
    #         }
    #     ]
    # }
    # }
    

    """调用 Stable Diffusion WebUI API"""
    payload = { 
        "init_images": [img_base64],
        "prompt": prompt_en.get("prompt", "") + "(masterpiece, best quality:1.2), highres, realistic,",
        "negative_prompt": prompt_en.get("negative_prompt", "")  + "blurry,  (worst quality, low quality:2),",
        "steps": params.get("steps", 30),
        "sampler_name": params.get("sampler_name", "Euler a"),
        "cfg_scale": params.get("cfg_scale", 7),
        "denoising_strength": params.get("denoising_strength", 0.75),        
        "height": params.get("height", 512),
        "width": params.get("width", 512),

        "alwayson_scripts":controlnet_args
    }

    response = requests.post(SD_API_URL, json=payload)
    result = response.json()
    return result["images"][0]

def download_and_convert(url: str) -> str:
    """下载 TIF 并转换为 PNG (base64)"""
    resp = requests.get(url, stream=True, verify=False)
    resp.raise_for_status()
    img = Image.open(io.BytesIO(resp.content)).convert("RGB")
    width, height = img.size
    width, height = getInputSize(width, height)
    buf = io.BytesIO()
    img.save(buf, format="PNG")
    img_base64 = base64.b64encode(buf.getvalue()).decode("utf-8")
    return img_base64, width, height

def getInputSize(width, height):
    max_side = max(width, height)
    if max_side > 2048:
        if width >= height:
            new_width = 2048
            new_height = int(height * (2048 / width))
        else:
            new_height = 2048
            new_width = int(width * (2048 / height))
        return new_width, new_height
    else:
        return width, height

def encode_file_to_base64(path):
    with open(path, 'rb') as file:
        return base64.b64encode(file.read()).decode('utf-8')
    
# ========== API ==========
@app.route("/generate", methods=["POST"])
def generate():
    data = request.get_json()
    prompt_cn = data["prompt"]
    params = data.get("params", {})

    # Download image
    if data.get("image_url", None) is not None:
        img_url = data["image_url"]
        img_base64, width, height = download_and_convert(img_url)
        params["width"], params["height"] = getInputSize(width, height)
    
    elif data.get("image", None) is not None:
        img_base64 = data["image"]
        params["width"], params["height"] = getInputSize(params["width"], params["height"])
  
    # 翻译 prompt
    prompt_en = {}
    for key, value in prompt_cn.items():
        prompt_en[key] = translate_prompt(value)        
    # prompt_en = translate_prompt(prompt_cn)

    # 调用 SD
    result_img_base64 = call_stable_diffusion(img_base64, prompt_en, params)

    return jsonify({"result": result_img_base64})

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=5001)
