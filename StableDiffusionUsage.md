# 传参说明：

## 传参方式
服务器端地址：http://10.20.174.41:5001  
服务器端请求地址：http://10.20.174.41:5001/generate  
请求方式：POST  
数据格式：JSON

## 参数说明
```JSON
    payload = {
        "image": "img_base64",
        "image_url": "img_url",
        "prompt": {PROMPT},
        "params": {PARAMS}
    }
```

### 1. image
***image 和 image_url***二选一即可。  
其中image需要输入的是base64，而image_url会在服务器上下载图像并转换为base64格式。推荐用image上传图像，可以节省图像下载和处理的时间。

### 2. prompt
prompt是文本描述，用于描述生成的图像内容。
prompt包括正向的文本(positive prompt)和负向的文本(negative prompt)  
输入的描述可以是中文，服务器端会自动转换为英文。  
正向提示词可以按照自己需要改进的方向去描述，负向提示词可以按照需要抑制的部分去描述。  
后续改进可以在此处引入
Qwen2-VL-7B-Instruct 来辅助生成正反向提示词，用户只需要输入中文描述即可自动生成正反向提示词。
```JSON
PROMPT = {
    "prompt": "现代化的城市， 干净的道路",
    "negative_prompt": "脏乱的环境，建筑工地，裸地"
}
```

### 3. params
params是用于控制生成图像的参数，包括：
```JSON
    params = {
        "steps": 30,
        "cfg_scale": 2.5,
        "sampler_name": "Euler a",
        "denoising_strength": 0.4,
}
```
- step为训练的轮数：  
    训练轮数和训练复杂度有关，训练轮数越多，生成的图像越复杂，但训练时间越长。  
    一般设置在20-50之间即可，设置过大会导致训练时间过长，设置过小会导致生成的图像不完整。  

- cfg_scale为分类器自由度：  
    **建议前端用滑动条来辅助设置**  
    值范围为[0,30]，一般设置在2-7.5之间  
    cfg_scale越大，生成的图像越符合prompt的描述，
    设置越小模型的自由发挥度越高。
    在模型天马行空的时候可以适当调大，当模型生成图像不够激进的时候适当调小  

- sampler_name为采样器名称：  
    建议不变

- denoising_strength为去噪强度：  
    **建议前端用滑动条来辅助设置**  
    值范围为[0,1]，一般设置在0.3-0.7之间  
    上传的模板图像的锚定程度，设定越小则和原图的相关度越强，设定越大则不会按照原图的形状来生图。
    在模型生成图片脱离了上传图像的时候需要调小，当图片没有自由发挥的时候需要调大。

# 返回参数
返回base64编码的图像
```JSON
{"result": result_img_base64}
```

# 服务器端启用说明
以下均在虚拟环境中运行：
1. 打开Anaconda Prompt
2. 激活虚拟环境：conda activate SAM
## 1. 启动stable diffusion web ui:
在虚拟环境中执行
```Python
# 进入目录
cd C:\03ai\sd.webui\webui
# 执行.bat
my-webui-user.bat
```
启用服务器后可以在服务器端通过http://127.0.0.1:7860/ 打开web界面

## 2. 启动服务器
重新打开一个虚拟环境：
1. 打开Anaconda Prompt
2. 激活虚拟环境：conda activate SAM
3. 执行命令```python C:\03ai\sd.webui run_sd.py ```