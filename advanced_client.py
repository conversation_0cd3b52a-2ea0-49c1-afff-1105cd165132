#!/usr/bin/env python3
"""
高级客户端示例 - 支持更多ControlNet功能和错误处理
包含图像预处理、批量处理、进度监控等功能
"""

import requests
import json
import base64
import io
import os
import time
import threading
from PIL import Image, ImageEnhance, ImageFilter
from typing import Optional, Dict, Any, List, Tuple
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedSDClient:
    def __init__(self, server_url: str = "http://localhost:7860"):
        self.base_url = server_url
        self.api_url = f"{server_url}/sdapi/v1"
        self.session = requests.Session()
        self.session.timeout = 300
        
    def preprocess_image(self, image_path: str, target_size: Tuple[int, int] = (512, 512)) -> Image.Image:
        """预处理输入图像"""
        image = Image.open(image_path).convert('RGB')
        
        # 调整大小保持比例
        image.thumbnail(target_size, Image.Resampling.LANCZOS)
        
        # 创建新图像并居中粘贴
        new_image = Image.new('RGB', target_size, (255, 255, 255))
        paste_x = (target_size[0] - image.width) // 2
        paste_y = (target_size[1] - image.height) // 2
        new_image.paste(image, (paste_x, paste_y))
        
        return new_image
    
    def enhance_image(self, image: Image.Image, brightness: float = 1.0, 
                     contrast: float = 1.0, sharpness: float = 1.0) -> Image.Image:
        """图像增强"""
        if brightness != 1.0:
            enhancer = ImageEnhance.Brightness(image)
            image = enhancer.enhance(brightness)
        
        if contrast != 1.0:
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(contrast)
            
        if sharpness != 1.0:
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(sharpness)
            
        return image
    
    def get_progress(self) -> Dict[str, Any]:
        """获取生成进度"""
        try:
            response = self.session.get(f"{self.api_url}/progress")
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"获取进度失败: {e}")
        return {}
    
    def monitor_progress(self, callback=None):
        """监控生成进度"""
        def monitor():
            while True:
                progress = self.get_progress()
                if callback:
                    callback(progress)
                
                if not progress.get('active', False):
                    break
                    
                time.sleep(1)
        
        thread = threading.Thread(target=monitor)
        thread.daemon = True
        thread.start()
        return thread
    
    def interrupt_generation(self):
        """中断生成过程"""
        try:
            response = self.session.post(f"{self.api_url}/interrupt")
            return response.status_code == 200
        except Exception as e:
            logger.error(f"中断失败: {e}")
            return False
    
    def get_available_models(self) -> Dict[str, List[str]]:
        """获取所有可用模型"""
        models = {}
        
        try:
            # SD模型
            response = self.session.get(f"{self.api_url}/sd-models")
            if response.status_code == 200:
                models['sd_models'] = [m['title'] for m in response.json()]
        except:
            models['sd_models'] = []
        
        try:
            # ControlNet模型
            response = self.session.get(f"{self.base_url}/controlnet/model_list")
            if response.status_code == 200:
                models['controlnet_models'] = response.json().get('model_list', [])
        except:
            models['controlnet_models'] = []
            
        try:
            # ControlNet预处理器
            response = self.session.get(f"{self.base_url}/controlnet/module_list")
            if response.status_code == 200:
                models['controlnet_modules'] = response.json().get('module_list', [])
        except:
            models['controlnet_modules'] = []
            
        return models
    
    def create_controlnet_unit(self, 
                              image_path: str,
                              control_type: str,
                              weight: float = 1.0,
                              guidance_start: float = 0.0,
                              guidance_end: float = 1.0,
                              processor_res: int = 512,
                              threshold_a: int = 64,
                              threshold_b: int = 64,
                              **kwargs) -> Dict[str, Any]:
        """创建ControlNet单元配置"""
        
        # 预定义的控制类型配置
        control_configs = {
            'depth': {
                'module': 'depth_midas',
                'model': 'control_v11f1p_sd15_depth [cfd03158]'
            },
            'lineart': {
                'module': 'lineart_coarse',
                'model': 'control_v11p_sd15_lineart [43d4be0d]'
            },
            'canny': {
                'module': 'canny',
                'model': 'control_v11p_sd15_canny [d14c016b]'
            },
            'openpose': {
                'module': 'openpose_full',
                'model': 'control_v11p_sd15_openpose [cab727d4]'
            },
            'scribble': {
                'module': 'scribble_pidinet',
                'model': 'control_v11p_sd15_scribble [d4ba51ff]'
            }
        }
        
        if control_type not in control_configs:
            raise ValueError(f"不支持的控制类型: {control_type}")
        
        config = control_configs[control_type]
        
        # 预处理图像
        image = self.preprocess_image(image_path)
        image_b64 = self.pil_to_base64(image)
        
        unit = {
            "input_image": image_b64,
            "module": config['module'],
            "model": config['model'],
            "weight": weight,
            "resize_mode": "Crop and Resize",
            "lowvram": False,
            "processor_res": processor_res,
            "threshold_a": threshold_a,
            "threshold_b": threshold_b,
            "guidance_start": guidance_start,
            "guidance_end": guidance_end,
            "control_mode": "Balanced",
            "pixel_perfect": False
        }
        
        # 添加额外参数
        unit.update(kwargs)
        return unit
    
    def batch_generate(self,
                      prompts: List[str],
                      controlnet_configs: List[Dict[str, Any]],
                      **common_params) -> List[Dict[str, Any]]:
        """批量生成图像"""
        results = []
        
        for i, prompt in enumerate(prompts):
            logger.info(f"处理第 {i+1}/{len(prompts)} 个请求")
            
            # 为每个请求创建ControlNet单元
            controlnet_units = []
            for config in controlnet_configs:
                if isinstance(config['image_path'], list):
                    # 如果是图像路径列表，使用对应索引的图像
                    image_path = config['image_path'][i % len(config['image_path'])]
                else:
                    # 如果是单个路径，所有请求使用同一图像
                    image_path = config['image_path']
                
                unit = self.create_controlnet_unit(
                    image_path=image_path,
                    control_type=config['control_type'],
                    weight=config.get('weight', 1.0),
                    guidance_start=config.get('guidance_start', 0.0),
                    guidance_end=config.get('guidance_end', 1.0)
                )
                controlnet_units.append(unit)
            
            # 生成图像
            result = self.txt2img_with_controlnet(
                prompt=prompt,
                controlnet_units=controlnet_units,
                **common_params
            )
            
            results.append(result)
            
            # 添加延迟避免服务器过载
            if i < len(prompts) - 1:
                time.sleep(1)
        
        return results
    
    def txt2img_with_controlnet(self,
                               prompt: str,
                               controlnet_units: List[Dict[str, Any]] = None,
                               negative_prompt: str = "",
                               width: int = 512,
                               height: int = 512,
                               steps: int = 20,
                               cfg_scale: float = 7.0,
                               sampler_name: str = "Euler a",
                               seed: int = -1,
                               **kwargs) -> Dict[str, Any]:
        """使用ControlNet生成图像"""
        
        payload = {
            "prompt": prompt,
            "negative_prompt": negative_prompt,
            "width": width,
            "height": height,
            "steps": steps,
            "cfg_scale": cfg_scale,
            "sampler_name": sampler_name,
            "seed": seed,
            "batch_size": 1,
            "n_iter": 1,
            "restore_faces": False,
            "tiling": False,
            "do_not_save_samples": True,
            "do_not_save_grid": True,
        }
        
        payload.update(kwargs)
        
        if controlnet_units:
            payload["alwayson_scripts"] = {
                "controlnet": {
                    "args": controlnet_units
                }
            }
        
        try:
            response = self.session.post(f"{self.api_url}/txt2img", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "images": result.get("images", []),
                    "parameters": result.get("parameters", {}),
                    "info": result.get("info", "")
                }
            else:
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"请求失败: {str(e)}"
            }
    
    def pil_to_base64(self, image: Image.Image) -> str:
        """PIL图像转base64"""
        buffer = io.BytesIO()
        image.save(buffer, format="PNG")
        return base64.b64encode(buffer.getvalue()).decode('utf-8')
    
    def base64_to_pil(self, base64_str: str) -> Image.Image:
        """base64转PIL图像"""
        image_data = base64.b64decode(base64_str)
        return Image.open(io.BytesIO(image_data))

# 使用示例
def main():
    client = AdvancedSDClient("http://192.168.1.100:7860")
    
    # 获取可用模型
    models = client.get_available_models()
    logger.info(f"可用模型: {models}")
    
    # 定义ControlNet配置
    controlnet_configs = [
        {
            'image_path': 'input/depth_image.jpg',
            'control_type': 'depth',
            'weight': 1.0,
            'guidance_start': 0.0,
            'guidance_end': 0.8
        },
        {
            'image_path': 'input/lineart_image.jpg', 
            'control_type': 'lineart',
            'weight': 0.8,
            'guidance_start': 0.2,
            'guidance_end': 1.0
        }
    ]
    
    # 创建ControlNet单元
    controlnet_units = []
    for config in controlnet_configs:
        unit = client.create_controlnet_unit(**config)
        controlnet_units.append(unit)
    
    # 监控进度
    def progress_callback(progress):
        if progress.get('active'):
            percent = progress.get('progress', 0) * 100
            logger.info(f"生成进度: {percent:.1f}%")
    
    monitor_thread = client.monitor_progress(progress_callback)
    
    # 生成图像
    result = client.txt2img_with_controlnet(
        prompt="a beautiful landscape with mountains and lake, highly detailed, 8k",
        negative_prompt="blurry, low quality, distorted",
        controlnet_units=controlnet_units,
        width=512,
        height=512,
        steps=25,
        cfg_scale=7.5,
        seed=42
    )
    
    if result["success"]:
        logger.info("图像生成成功!")
        # 保存图像
        for i, img_b64 in enumerate(result["images"]):
            image = client.base64_to_pil(img_b64)
            image.save(f"output/generated_{int(time.time())}_{i}.png")
    else:
        logger.error(f"生成失败: {result['error']}")

if __name__ == "__main__":
    main()
